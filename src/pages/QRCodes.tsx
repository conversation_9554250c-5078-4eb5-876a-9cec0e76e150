import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/layout/AppSidebar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { QrCode, Plus, Download, Eye, Copy } from "lucide-react";

const QRCodes = () => {
  const qrCodes = [
    {
      id: 1,
      name: "Table Feedback",
      description: "Customer feedback form for dining tables",
      scans: 245,
      created: "2 weeks ago",
      location: "Downtown Restaurant"
    },
    {
      id: 2,
      name: "Newsletter Signup",
      description: "Subscribe to our newsletter and promotions",
      scans: 123,
      created: "1 week ago",
      location: "Mall Location"
    }
  ];

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        <AppSidebar />
        <main className="flex-1 flex flex-col">
          <header className="h-16 border-b border-border bg-background flex items-center px-6">
            <SidebarTrigger />
            <div className="ml-4 flex-1">
              <h1 className="text-xl font-semibold text-foreground">QR Codes</h1>
              <p className="text-sm text-muted-foreground">Manage your QR code campaigns</p>
            </div>
            <Button className="bg-gradient-primary">
              <Plus className="w-4 h-4 mr-2" />
              Create QR Code
            </Button>
          </header>

          <div className="flex-1 p-6 space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {qrCodes.map((qr) => (
                <Card key={qr.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <QrCode className="w-8 h-8 text-primary" />
                      <span className="text-2xl font-bold text-muted-foreground">{qr.scans}</span>
                    </div>
                    <CardTitle className="text-lg">{qr.name}</CardTitle>
                    <CardDescription>{qr.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="text-sm text-muted-foreground">
                        <p>Location: {qr.location}</p>
                        <p>Created: {qr.created}</p>
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline" className="flex-1">
                          <Eye className="w-3 h-3 mr-1" />
                          View
                        </Button>
                        <Button size="sm" variant="outline">
                          <Copy className="w-3 h-3" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Download className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {/* Create New Card */}
              <Card className="border-dashed border-2 hover:border-primary transition-colors cursor-pointer">
                <CardContent className="flex flex-col items-center justify-center h-full min-h-[200px] text-center">
                  <QrCode className="w-12 h-12 text-muted-foreground mb-4" />
                  <h3 className="font-medium mb-2">Create New QR Code</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Generate a new QR code for customer engagement
                  </p>
                  <Button className="bg-gradient-primary">
                    <Plus className="w-4 h-4 mr-2" />
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
      </div>
    </SidebarProvider>
  );
};

export default QRCodes;