import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/layout/AppSidebar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, QrCode, MessageSquare, TrendingUp } from "lucide-react";

const Dashboard = () => {
  const stats = [
    {
      title: "Total Customers",
      value: "2,456",
      change: "+12.5%",
      changeType: "positive" as const,
      icon: Users,
    },
    {
      title: "QR Code Scans",
      value: "1,234",
      change: "+8.2%", 
      changeType: "positive" as const,
      icon: QrCode,
    },
    {
      title: "Messages Sent",
      value: "5,678",
      change: "+15.3%",
      changeType: "positive" as const,
      icon: MessageSquare,
    },
    {
      title: "Conversion Rate",
      value: "23.4%",
      change: "+2.1%",
      changeType: "positive" as const,
      icon: TrendingUp,
    },
  ];

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        <AppSidebar />
        <main className="flex-1 flex flex-col">
          <header className="h-16 border-b border-border bg-background flex items-center px-6">
            <SidebarTrigger />
            <div className="ml-4">
              <h1 className="text-xl font-semibold text-foreground">Dashboard</h1>
              <p className="text-sm text-muted-foreground">Overview of your business performance</p>
            </div>
          </header>

          <div className="flex-1 p-6 space-y-6">
            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {stats.map((stat) => (
                <Card key={stat.title} className="hover:shadow-md transition-shadow">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">
                      {stat.title}
                    </CardTitle>
                    <stat.icon className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stat.value}</div>
                    <p className="text-xs text-brand-accent font-medium">
                      {stat.change} from last month
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Customer Activity</CardTitle>
                  <CardDescription>Latest customer interactions and sign-ups</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[1, 2, 3, 4].map((i) => (
                      <div key={i} className="flex items-center space-x-4">
                        <div className="w-2 h-2 bg-primary rounded-full"></div>
                        <div className="flex-1">
                          <p className="text-sm font-medium">New customer signed up via QR code</p>
                          <p className="text-xs text-muted-foreground">2 minutes ago</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                  <CardDescription>Common tasks and shortcuts</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <button className="p-4 border rounded-lg hover:bg-accent text-left">
                      <QrCode className="h-6 w-6 mb-2 text-primary" />
                      <p className="font-medium">Generate QR Code</p>
                      <p className="text-xs text-muted-foreground">Create new QR codes</p>
                    </button>
                    <button className="p-4 border rounded-lg hover:bg-accent text-left">
                      <MessageSquare className="h-6 w-6 mb-2 text-primary" />
                      <p className="font-medium">Send Campaign</p>
                      <p className="text-xs text-muted-foreground">Broadcast messages</p>
                    </button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
      </div>
    </SidebarProvider>
  );
};

export default Dashboard;