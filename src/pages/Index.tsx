import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Building2, QrCode, Users, Workflow, ArrowRight, CheckCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";

const Index = () => {
  const navigate = useNavigate();

  const features = [
    {
      icon: Users,
      title: "Customer Management",
      description: "Organize and track all your customer interactions in one place"
    },
    {
      icon: QrCode,
      title: "QR Code Generation",
      description: "Create custom QR codes to capture customer data effortlessly"
    },
    {
      icon: Workflow,
      title: "Automated Workflows",
      description: "Set up automated email and SMS campaigns to engage customers"
    }
  ];

  const benefits = [
    "Multi-location support for growing businesses",
    "Real-time analytics and reporting",
    "Email and SMS automation",
    "Mobile-responsive design",
    "Role-based access control"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-brand-secondary/10 to-primary/5">
      {/* <PERSON><PERSON> */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 h-16 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
              <Building2 className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold">BizPulse</span>
          </div>
          <Button onClick={() => navigate('/login')} className="bg-gradient-primary">
            Sign In
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </header>

      {/* Hero Section */}
      <section className="container mx-auto px-4 py-20 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-5xl font-bold mb-6 bg-gradient-to-r from-primary via-primary to-brand-accent bg-clip-text text-transparent">
            Transform Your Customer Relationships
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            BizPulse is the complete CRM solution for restaurants and service businesses. 
            Capture leads with QR codes, automate communications, and grow your customer base.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" onClick={() => navigate('/dashboard')} className="bg-gradient-primary text-lg px-8">
              View Demo Dashboard
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
            <Button size="lg" variant="outline">
              Watch Demo
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-4 py-20">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold mb-4">Everything You Need to Grow</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Powerful features designed specifically for restaurants and service businesses
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {features.map((feature, index) => (
            <Card key={index} className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <feature.icon className="w-6 h-6 text-primary" />
                </div>
                <CardTitle className="text-xl">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">{feature.description}</CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Benefits List */}
        <div className="max-w-2xl mx-auto">
          <h3 className="text-2xl font-bold text-center mb-8">Why Choose BizPulse?</h3>
          <div className="space-y-4">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-center gap-3">
                <CheckCircle className="w-5 h-5 text-brand-accent flex-shrink-0" />
                <span className="text-muted-foreground">{benefit}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-primary text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
          <p className="text-xl mb-8 opacity-90">
            Join hundreds of businesses already using BizPulse to grow their customer base
          </p>
          <Button size="lg" variant="secondary" onClick={() => navigate('/login')}>
            Start Your Free Trial
            <ArrowRight className="w-5 h-5 ml-2" />
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t bg-background py-12">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center gap-2 mb-4">
            <div className="w-6 h-6 bg-gradient-primary rounded flex items-center justify-center">
              <Building2 className="w-4 h-4 text-white" />
            </div>
            <span className="font-bold">BizPulse</span>
          </div>
          <p className="text-muted-foreground">
            © 2024 BizPulse. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Index;
