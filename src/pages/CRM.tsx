import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/layout/AppSidebar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Search, Plus, Filter, Mail, Phone, MapPin } from "lucide-react";

const CRM = () => {
  const customers = [
    {
      id: 1,
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "+****************",
      source: "QR Code",
      location: "Downtown Restaurant",
      lastVisit: "2 days ago",
      status: "Active"
    },
    {
      id: 2,
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "+****************",
      source: "Manual Entry",
      location: "Mall Location",
      lastVisit: "1 week ago",
      status: "Active"
    },
    {
      id: 3,
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "+****************",
      source: "QR Code",
      location: "Downtown Restaurant",
      lastVisit: "3 days ago",
      status: "New"
    }
  ];

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        <AppSidebar />
        <main className="flex-1 flex flex-col">
          <header className="h-16 border-b border-border bg-background flex items-center px-6">
            <SidebarTrigger />
            <div className="ml-4 flex-1">
              <h1 className="text-xl font-semibold text-foreground">Customer Management</h1>
              <p className="text-sm text-muted-foreground">Manage your customer database</p>
            </div>
            <Button className="bg-gradient-primary">
              <Plus className="w-4 h-4 mr-2" />
              Add Customer
            </Button>
          </header>

          <div className="flex-1 p-6 space-y-6">
            {/* Search and Filters */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search customers..."
                      className="pl-10"
                    />
                  </div>
                  <Button variant="outline">
                    <Filter className="w-4 h-4 mr-2" />
                    Filters
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Customer List */}
            <Card>
              <CardHeader>
                <CardTitle>Customers ({customers.length})</CardTitle>
                <CardDescription>Your customer database</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {customers.map((customer) => (
                    <div key={customer.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-primary">
                            {customer.name.split(' ').map(n => n[0]).join('')}
                          </span>
                        </div>
                        <div>
                          <h3 className="font-medium">{customer.name}</h3>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Mail className="w-3 h-3" />
                              {customer.email}
                            </div>
                            <div className="flex items-center gap-1">
                              <Phone className="w-3 h-3" />
                              {customer.phone}
                            </div>
                            <div className="flex items-center gap-1">
                              <MapPin className="w-3 h-3" />
                              {customer.location}
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <Badge variant={customer.status === 'New' ? 'default' : 'secondary'}>
                            {customer.status}
                          </Badge>
                          <p className="text-xs text-muted-foreground mt-1">
                            Last visit: {customer.lastVisit}
                          </p>
                        </div>
                        <Button variant="outline" size="sm">
                          View
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </SidebarProvider>
  );
};

export default CRM;