import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/layout/AppSidebar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FileText, Plus, Edit, Copy, Mail, MessageSquare } from "lucide-react";

const Templates = () => {
  const templates = [
    {
      id: 1,
      name: "Welcome Email",
      description: "Welcome new customers to your business",
      type: "Email",
      category: "Onboarding",
      lastModified: "2 days ago",
      usage: 45
    },
    {
      id: 2,
      name: "Feedback Request",
      description: "Ask customers for feedback and reviews",
      type: "SMS",
      category: "Feedback",
      lastModified: "1 week ago",
      usage: 23
    },
    {
      id: 3,
      name: "Birthday Offer",
      description: "Special birthday discount for customers",
      type: "Email",
      category: "Promotions",
      lastModified: "3 days ago",
      usage: 12
    }
  ];

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        <AppSidebar />
        <main className="flex-1 flex flex-col">
          <header className="h-16 border-b border-border bg-background flex items-center px-6">
            <SidebarTrigger />
            <div className="ml-4 flex-1">
              <h1 className="text-xl font-semibold text-foreground">Templates</h1>
              <p className="text-sm text-muted-foreground">Manage your email and SMS templates</p>
            </div>
            <Button className="bg-gradient-primary">
              <Plus className="w-4 h-4 mr-2" />
              Create Template
            </Button>
          </header>

          <div className="flex-1 p-6 space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {templates.map((template) => (
                <Card key={template.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {template.type === 'Email' ? (
                          <Mail className="w-5 h-5 text-primary" />
                        ) : (
                          <MessageSquare className="w-5 h-5 text-primary" />
                        )}
                        <Badge variant="outline">{template.type}</Badge>
                      </div>
                      <span className="text-sm text-muted-foreground">{template.usage} uses</span>
                    </div>
                    <CardTitle className="text-lg">{template.name}</CardTitle>
                    <CardDescription>{template.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Category:</span>
                        <Badge variant="secondary">{template.category}</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Modified:</span>
                        <span>{template.lastModified}</span>
                      </div>
                      <div className="flex gap-2 pt-2">
                        <Button size="sm" variant="outline" className="flex-1">
                          <Edit className="w-3 h-3 mr-1" />
                          Edit
                        </Button>
                        <Button size="sm" variant="outline">
                          <Copy className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {/* Create New Template Card */}
              <Card className="border-dashed border-2 hover:border-primary transition-colors cursor-pointer">
                <CardContent className="flex flex-col items-center justify-center h-full min-h-[200px] text-center">
                  <FileText className="w-12 h-12 text-muted-foreground mb-4" />
                  <h3 className="font-medium mb-2">Create New Template</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Design reusable email and SMS templates
                  </p>
                  <Button className="bg-gradient-primary">
                    <Plus className="w-4 h-4 mr-2" />
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
      </div>
    </SidebarProvider>
  );
};

export default Templates;