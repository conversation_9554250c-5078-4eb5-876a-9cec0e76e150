import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/layout/AppSidebar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Workflow, Plus, Play, Pause, Edit } from "lucide-react";

const Automations = () => {
  const automations = [
    {
      id: 1,
      name: "Welcome Email Series",
      description: "Send welcome emails to new customers",
      trigger: "New customer signup",
      status: "Active",
      lastRun: "2 hours ago",
      successRate: "94%"
    },
    {
      id: 2,
      name: "Feedback Follow-up",
      description: "Request feedback 3 days after visit",
      trigger: "Customer visit",
      status: "Active", 
      lastRun: "1 day ago",
      successRate: "87%"
    },
    {
      id: 3,
      name: "Birthday Campaign",
      description: "Send birthday offers to customers",
      trigger: "Customer birthday",
      status: "Paused",
      lastRun: "1 week ago",
      successRate: "76%"
    }
  ];

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        <AppSidebar />
        <main className="flex-1 flex flex-col">
          <header className="h-16 border-b border-border bg-background flex items-center px-6">
            <SidebarTrigger />
            <div className="ml-4 flex-1">
              <h1 className="text-xl font-semibold text-foreground">Automations</h1>
              <p className="text-sm text-muted-foreground">Manage your automated workflows</p>
            </div>
            <Button className="bg-gradient-primary">
              <Plus className="w-4 h-4 mr-2" />
              Create Automation
            </Button>
          </header>

          <div className="flex-1 p-6 space-y-6">
            <div className="space-y-4">
              {automations.map((automation) => (
                <Card key={automation.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                          <Workflow className="w-5 h-5 text-primary" />
                        </div>
                        <div>
                          <h3 className="font-medium text-lg">{automation.name}</h3>
                          <p className="text-sm text-muted-foreground">{automation.description}</p>
                          <div className="flex items-center gap-4 mt-2">
                            <span className="text-xs text-muted-foreground">
                              Trigger: {automation.trigger}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              Last run: {automation.lastRun}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              Success rate: {automation.successRate}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Badge variant={automation.status === 'Active' ? 'default' : 'secondary'}>
                          {automation.status}
                        </Badge>
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline">
                            <Edit className="w-3 h-3" />
                          </Button>
                          <Button size="sm" variant="outline">
                            {automation.status === 'Active' ? (
                              <Pause className="w-3 h-3" />
                            ) : (
                              <Play className="w-3 h-3" />
                            )}
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Empty State */}
            {automations.length === 0 && (
              <Card className="border-dashed border-2">
                <CardContent className="flex flex-col items-center justify-center py-12 text-center">
                  <Workflow className="w-16 h-16 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No automations yet</h3>
                  <p className="text-muted-foreground mb-6 max-w-md">
                    Create your first automation to streamline customer communications and improve engagement.
                  </p>
                  <Button className="bg-gradient-primary">
                    <Plus className="w-4 h-4 mr-2" />
                    Create Your First Automation
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </main>
      </div>
    </SidebarProvider>
  );
};

export default Automations;